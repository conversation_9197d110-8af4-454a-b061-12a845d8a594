"""
Advanced Statistical Analysis for Journal Manuscript:
"The Role of International Student Diversity in University Excellence: Evidence from 1,500 Institutions in the 2026 QS Rankings"

Author: Dr. <PERSON>, Symbiosis International (Deemed University)
Created: 2025-01-08
Purpose: Comprehensive statistical analysis for peer-reviewed journal submission
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import pearsonr, spearmanr
from sklearn.preprocessing import StandardScaler
from sklearn.impute import KNNImputer
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score
import statsmodels.api as sm
from statsmodels.stats.outliers_influence import variance_inflation_factor
from statsmodels.stats.diagnostic import het_breuschpagan
from statsmodels.stats.stattools import jarque_bera
import warnings
warnings.filterwarnings('ignore')

# Set publication-ready style
plt.style.use('seaborn-v0_8-whitegrid')
plt.rcParams.update({
    'font.family': 'Times New Roman',
    'font.size': 12,
    'axes.titlesize': 14,
    'axes.labelsize': 12,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 10,
    'figure.titlesize': 16,
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'savefig.bbox': 'tight',
    'savefig.transparent': False
})

class ManuscriptAnalysis:
    """
    Comprehensive statistical analysis class for journal manuscript.
    Focuses on international student diversity and university excellence.
    """
    
    def __init__(self, filepath):
        """Initialize with dataset loading and preprocessing."""
        self.filepath = filepath
        self.df = None
        self.df_clean = None
        self.results = {}
        self.figures = {}
        self.tables = {}
        
    def load_and_preprocess_data(self):
        """Load and preprocess the QS rankings dataset."""
        print("="*80)
        print("LOADING AND PREPROCESSING QS RANKINGS DATA")
        print("="*80)
        
        # Load dataset
        self.df = pd.read_csv(self.filepath)
        print(f"Original dataset: {self.df.shape[0]:,} universities × {self.df.shape[1]} variables")
        
        # Define key variables for analysis
        self.diversity_vars = [
            'International_Students_Diversity_Score',
            'International_Students_Score', 
            'International_Faculty_Score'
        ]
        
        self.outcome_vars = [
            'Overall_Score',
            'Academic_Reputation_Score',
            'Employer_Reputation_Score',
            'Citations_per_Faculty_Score',
            'Employment_Outcomes_Score'
        ]
        
        self.control_vars = [
            'Size', 'Focus', 'Research', 'PrivateGovernment', 'Country'
        ]
        
        # Create analysis subset
        analysis_vars = (self.diversity_vars + self.outcome_vars + 
                        self.control_vars + ['Institution_Name', 'Rank_2026'])
        
        self.df_analysis = self.df[analysis_vars].copy()
        
        # Handle missing data strategically
        self._handle_missing_data()
        
        # Create derived variables
        self._create_derived_variables()
        
        print(f"Analysis dataset: {self.df_clean.shape[0]:,} universities × {self.df_clean.shape[1]} variables")
        print("✓ Data preprocessing completed")
        
    def _handle_missing_data(self):
        """Strategic handling of missing data for manuscript."""
        print("\nHandling missing data...")
        
        # Analyze missing patterns
        missing_summary = self.df_analysis.isnull().sum()
        missing_pct = (missing_summary / len(self.df_analysis)) * 100
        
        print("Missing data patterns:")
        for var in missing_summary[missing_summary > 0].index:
            print(f"  {var}: {missing_summary[var]} ({missing_pct[var]:.1f}%)")
        
        # Strategy 1: Complete case analysis for primary variables
        primary_vars = self.diversity_vars + ['Overall_Score']
        self.df_complete = self.df_analysis.dropna(subset=primary_vars)
        
        # Strategy 2: Multiple imputation for sensitivity analysis
        numeric_cols = self.df_analysis.select_dtypes(include=[np.number]).columns
        imputer = KNNImputer(n_neighbors=5)
        
        df_numeric_imputed = pd.DataFrame(
            imputer.fit_transform(self.df_analysis[numeric_cols]),
            columns=numeric_cols,
            index=self.df_analysis.index
        )
        
        # Combine imputed numeric with original categorical
        self.df_imputed = self.df_analysis.copy()
        self.df_imputed[numeric_cols] = df_numeric_imputed
        
        # Use complete case analysis as primary approach
        self.df_clean = self.df_complete.copy()
        
        print(f"Complete case analysis: {len(self.df_clean)} universities")
        print(f"Imputed dataset: {len(self.df_imputed)} universities")
        
    def _create_derived_variables(self):
        """Create derived variables for analysis."""
        # International diversity composite score
        diversity_scores = self.df_clean[self.diversity_vars].fillna(0)
        self.df_clean['International_Diversity_Composite'] = diversity_scores.mean(axis=1)
        
        # Standardize continuous variables for effect size interpretation
        scaler = StandardScaler()
        continuous_vars = self.df_clean.select_dtypes(include=[np.number]).columns
        
        for var in continuous_vars:
            if var not in ['Rank_2026']:  # Don't standardize rank
                self.df_clean[f'{var}_std'] = scaler.fit_transform(
                    self.df_clean[[var]]
                ).flatten()
        
        # Create categorical dummy variables
        categorical_vars = ['Size', 'Focus', 'Research', 'PrivateGovernment']
        for var in categorical_vars:
            if var in self.df_clean.columns:
                dummies = pd.get_dummies(self.df_clean[var], prefix=var, drop_first=True)
                self.df_clean = pd.concat([self.df_clean, dummies], axis=1)
        
        print("✓ Derived variables created")
        
    def descriptive_statistics(self):
        """Generate comprehensive descriptive statistics (Table 1)."""
        print("\n" + "="*60)
        print("GENERATING DESCRIPTIVE STATISTICS")
        print("="*60)
        
        # Key variables for descriptive table
        key_vars = self.diversity_vars + self.outcome_vars
        if 'International_Diversity_Composite' in self.df_clean.columns:
            key_vars.append('International_Diversity_Composite')

        desc_stats = []

        for var in key_vars:
            if var in self.df_clean.columns:
                data = self.df_clean[var].dropna()

                # Handle missing data calculation
                if var in self.df_analysis.columns:
                    missing_n = self.df_analysis[var].isnull().sum()
                    missing_pct = (missing_n / len(self.df_analysis)) * 100
                else:
                    # For derived variables, calculate from clean dataset
                    missing_n = len(self.df_clean) - len(data)
                    missing_pct = (missing_n / len(self.df_clean)) * 100

                stats_dict = {
                    'Variable': var,
                    'N': len(data),
                    'Mean': data.mean(),
                    'SD': data.std(),
                    'Min': data.min(),
                    'Max': data.max(),
                    'Skewness': stats.skew(data),
                    'Kurtosis': stats.kurtosis(data),
                    'Missing_N': missing_n,
                    'Missing_Pct': missing_pct
                }
                desc_stats.append(stats_dict)
        
        self.tables['descriptive_stats'] = pd.DataFrame(desc_stats)
        
        # Categorical variables summary
        cat_summary = []
        for var in ['Size', 'Focus', 'Research', 'PrivateGovernment']:
            if var in self.df_clean.columns:
                value_counts = self.df_clean[var].value_counts()
                for category, count in value_counts.items():
                    cat_summary.append({
                        'Variable': var,
                        'Category': category,
                        'N': count,
                        'Percentage': (count / len(self.df_clean)) * 100
                    })
        
        self.tables['categorical_summary'] = pd.DataFrame(cat_summary)
        
        print("✓ Descriptive statistics completed")
        return self.tables['descriptive_stats']
        
    def correlation_analysis(self):
        """Comprehensive correlation analysis (Table 2)."""
        print("\n" + "="*60)
        print("CORRELATION ANALYSIS")
        print("="*60)
        
        # Select variables for correlation matrix
        corr_vars = (self.diversity_vars + self.outcome_vars + 
                    ['International_Diversity_Composite'])
        
        # Filter available variables
        available_vars = [var for var in corr_vars if var in self.df_clean.columns]
        corr_data = self.df_clean[available_vars].dropna()
        
        # Pearson correlations
        corr_matrix = corr_data.corr()
        
        # Calculate confidence intervals and p-values
        n = len(corr_data)
        corr_details = []
        
        for i, var1 in enumerate(available_vars):
            for j, var2 in enumerate(available_vars):
                if i < j:  # Upper triangle only
                    r, p_val = pearsonr(corr_data[var1], corr_data[var2])
                    
                    # 95% confidence interval for correlation
                    z = np.arctanh(r)
                    se = 1 / np.sqrt(n - 3)
                    ci_lower = np.tanh(z - 1.96 * se)
                    ci_upper = np.tanh(z + 1.96 * se)
                    
                    corr_details.append({
                        'Variable_1': var1,
                        'Variable_2': var2,
                        'r': r,
                        'p_value': p_val,
                        'CI_lower': ci_lower,
                        'CI_upper': ci_upper,
                        'n': n,
                        'Significance': '***' if p_val < 0.001 else '**' if p_val < 0.01 else '*' if p_val < 0.05 else ''
                    })
        
        self.tables['correlation_matrix'] = corr_matrix
        self.tables['correlation_details'] = pd.DataFrame(corr_details)
        
        print(f"✓ Correlation analysis completed ({len(corr_details)} correlations)")
        return corr_matrix
        
    def hierarchical_regression_analysis(self):
        """Hierarchical multiple regression analysis (Table 3)."""
        print("\n" + "="*60)
        print("HIERARCHICAL REGRESSION ANALYSIS")
        print("="*60)

        # Prepare data for regression - focus on available numeric variables
        numeric_diversity_vars = [var for var in self.diversity_vars if var in self.df_clean.columns]

        if 'Overall_Score' not in self.df_clean.columns:
            print("Overall_Score not available for regression analysis")
            return None

        reg_data = self.df_clean.dropna(subset=['Overall_Score'] + numeric_diversity_vars)

        if len(reg_data) < 30:
            print(f"Warning: Small sample size for regression analysis (N={len(reg_data)})")
            if len(reg_data) < 10:
                print("Sample too small for regression analysis")
                return None

        # Define regression models
        models = {}

        # Simple model: Just diversity variables
        if numeric_diversity_vars:
            try:
                X_simple = reg_data[numeric_diversity_vars].select_dtypes(include=[np.number])
                X_simple = sm.add_constant(X_simple)
                y = reg_data['Overall_Score']

                # Ensure no missing values
                combined_data = pd.concat([X_simple, y], axis=1).dropna()
                if len(combined_data) >= 10:
                    X_simple_clean = combined_data.iloc[:, :-1]
                    y_clean = combined_data.iloc[:, -1]

                    model_simple = sm.OLS(y_clean, X_simple_clean).fit()
                    models['Model_1_Diversity'] = model_simple
                    print(f"✓ Model 1 (Diversity only): N={len(combined_data)}")

            except Exception as e:
                print(f"Error in simple model: {e}")

        # Composite model if available
        if 'International_Diversity_Composite' in reg_data.columns:
            try:
                X_composite = reg_data[['International_Diversity_Composite']].select_dtypes(include=[np.number])
                X_composite = sm.add_constant(X_composite)
                y = reg_data['Overall_Score']

                # Ensure no missing values
                combined_data = pd.concat([X_composite, y], axis=1).dropna()
                if len(combined_data) >= 10:
                    X_composite_clean = combined_data.iloc[:, :-1]
                    y_clean = combined_data.iloc[:, -1]

                    model_composite = sm.OLS(y_clean, X_composite_clean).fit()
                    models['Model_2_Composite'] = model_composite
                    print(f"✓ Model 2 (Composite): N={len(combined_data)}")

            except Exception as e:
                print(f"Error in composite model: {e}")
        
        # Store regression results
        self.results['regression_models'] = models

        if not models:
            print("No regression models could be fitted")
            return None

        # Create regression results table
        reg_results = []
        for model_name, model in models.items():
            try:
                for param in model.params.index:
                    if param != 'const':
                        # Calculate standardized beta if possible
                        try:
                            if param in reg_data.columns:
                                beta = model.params[param] * (reg_data[param].std() / reg_data['Overall_Score'].std())
                            else:
                                beta = np.nan
                        except:
                            beta = np.nan

                        reg_results.append({
                            'Model': model_name,
                            'Variable': param,
                            'Coefficient': model.params[param],
                            'SE': model.bse[param],
                            'Beta': beta,
                            't_value': model.tvalues[param],
                            'p_value': model.pvalues[param],
                            'CI_lower': model.conf_int().loc[param, 0],
                            'CI_upper': model.conf_int().loc[param, 1]
                        })

                # Add model statistics
                reg_results.append({
                    'Model': model_name,
                    'Variable': 'Model_Stats',
                    'Coefficient': np.nan,
                    'SE': np.nan,
                    'Beta': np.nan,
                    't_value': np.nan,
                    'p_value': np.nan,
                    'CI_lower': np.nan,
                    'CI_upper': np.nan,
                    'R_squared': model.rsquared,
                    'Adj_R_squared': model.rsquared_adj,
                    'F_statistic': model.fvalue,
                    'F_p_value': model.f_pvalue,
                    'N': int(model.nobs)
                })

            except Exception as e:
                print(f"Error processing model {model_name}: {e}")

        self.tables['regression_results'] = pd.DataFrame(reg_results)

        print(f"✓ Hierarchical regression completed ({len(models)} models)")
        return models

    def subgroup_analysis(self):
        """Subgroup analysis by institutional characteristics (Table 4)."""
        print("\n" + "="*60)
        print("SUBGROUP ANALYSIS")
        print("="*60)

        subgroup_results = []

        # Analysis by institutional size
        if 'Size' in self.df_clean.columns:
            for size_category in self.df_clean['Size'].unique():
                if pd.notna(size_category):
                    subset = self.df_clean[self.df_clean['Size'] == size_category]

                    if len(subset) >= 10:  # Minimum sample size
                        # Correlation between diversity and excellence
                        if ('International_Diversity_Composite' in subset.columns and
                            'Overall_Score' in subset.columns):

                            valid_data = subset[['International_Diversity_Composite', 'Overall_Score']].dropna()

                            if len(valid_data) >= 10:
                                r, p_val = pearsonr(valid_data['International_Diversity_Composite'],
                                                  valid_data['Overall_Score'])

                                subgroup_results.append({
                                    'Characteristic': 'Size',
                                    'Category': size_category,
                                    'N': len(valid_data),
                                    'Mean_Diversity': valid_data['International_Diversity_Composite'].mean(),
                                    'Mean_Excellence': valid_data['Overall_Score'].mean(),
                                    'Correlation_r': r,
                                    'Correlation_p': p_val,
                                    'Effect_Size': 'Small' if abs(r) < 0.3 else 'Medium' if abs(r) < 0.5 else 'Large'
                                })

        # Analysis by research intensity
        if 'Research' in self.df_clean.columns:
            for research_level in self.df_clean['Research'].unique():
                if pd.notna(research_level):
                    subset = self.df_clean[self.df_clean['Research'] == research_level]

                    if len(subset) >= 10:
                        if ('International_Diversity_Composite' in subset.columns and
                            'Overall_Score' in subset.columns):

                            valid_data = subset[['International_Diversity_Composite', 'Overall_Score']].dropna()

                            if len(valid_data) >= 10:
                                r, p_val = pearsonr(valid_data['International_Diversity_Composite'],
                                                  valid_data['Overall_Score'])

                                subgroup_results.append({
                                    'Characteristic': 'Research_Intensity',
                                    'Category': research_level,
                                    'N': len(valid_data),
                                    'Mean_Diversity': valid_data['International_Diversity_Composite'].mean(),
                                    'Mean_Excellence': valid_data['Overall_Score'].mean(),
                                    'Correlation_r': r,
                                    'Correlation_p': p_val,
                                    'Effect_Size': 'Small' if abs(r) < 0.3 else 'Medium' if abs(r) < 0.5 else 'Large'
                                })

        # Analysis by ownership type
        if 'PrivateGovernment' in self.df_clean.columns:
            for ownership in self.df_clean['PrivateGovernment'].unique():
                if pd.notna(ownership):
                    subset = self.df_clean[self.df_clean['PrivateGovernment'] == ownership]

                    if len(subset) >= 10:
                        if ('International_Diversity_Composite' in subset.columns and
                            'Overall_Score' in subset.columns):

                            valid_data = subset[['International_Diversity_Composite', 'Overall_Score']].dropna()

                            if len(valid_data) >= 10:
                                r, p_val = pearsonr(valid_data['International_Diversity_Composite'],
                                                  valid_data['Overall_Score'])

                                subgroup_results.append({
                                    'Characteristic': 'Ownership',
                                    'Category': ownership,
                                    'N': len(valid_data),
                                    'Mean_Diversity': valid_data['International_Diversity_Composite'].mean(),
                                    'Mean_Excellence': valid_data['Overall_Score'].mean(),
                                    'Correlation_r': r,
                                    'Correlation_p': p_val,
                                    'Effect_Size': 'Small' if abs(r) < 0.3 else 'Medium' if abs(r) < 0.5 else 'Large'
                                })

        self.tables['subgroup_analysis'] = pd.DataFrame(subgroup_results)

        print(f"✓ Subgroup analysis completed ({len(subgroup_results)} groups)")
        return self.tables['subgroup_analysis']

    def geographic_analysis(self):
        """Geographic analysis by country/region (Table 5)."""
        print("\n" + "="*60)
        print("GEOGRAPHIC ANALYSIS")
        print("="*60)

        if 'Country' not in self.df_clean.columns:
            print("Country variable not available for geographic analysis")
            return None

        # Country-level analysis
        country_results = []
        country_counts = self.df_clean['Country'].value_counts()

        # Focus on countries with at least 5 universities
        top_countries = country_counts[country_counts >= 5].index

        for country in top_countries:
            subset = self.df_clean[self.df_clean['Country'] == country]

            # Calculate country-level statistics
            country_stats = {
                'Country': country,
                'N_Universities': len(subset),
                'Mean_Overall_Score': subset['Overall_Score'].mean() if 'Overall_Score' in subset.columns else np.nan,
                'SD_Overall_Score': subset['Overall_Score'].std() if 'Overall_Score' in subset.columns else np.nan,
                'Mean_Diversity_Score': subset['International_Diversity_Composite'].mean() if 'International_Diversity_Composite' in subset.columns else np.nan,
                'SD_Diversity_Score': subset['International_Diversity_Composite'].std() if 'International_Diversity_Composite' in subset.columns else np.nan,
                'Top_100_Count': len(subset[subset['Rank_2026'] <= 100]) if 'Rank_2026' in subset.columns else 0,
                'Top_500_Count': len(subset[subset['Rank_2026'] <= 500]) if 'Rank_2026' in subset.columns else 0
            }

            # Calculate correlation within country
            if ('International_Diversity_Composite' in subset.columns and
                'Overall_Score' in subset.columns):

                valid_data = subset[['International_Diversity_Composite', 'Overall_Score']].dropna()

                if len(valid_data) >= 5:
                    r, p_val = pearsonr(valid_data['International_Diversity_Composite'],
                                      valid_data['Overall_Score'])
                    country_stats['Within_Country_Correlation'] = r
                    country_stats['Within_Country_P_Value'] = p_val
                else:
                    country_stats['Within_Country_Correlation'] = np.nan
                    country_stats['Within_Country_P_Value'] = np.nan

            country_results.append(country_stats)

        self.tables['geographic_analysis'] = pd.DataFrame(country_results)

        # Sort by number of universities and mean overall score
        self.tables['geographic_analysis'] = self.tables['geographic_analysis'].sort_values(
            ['Mean_Overall_Score'], ascending=False, na_position='last'
        )

        print(f"✓ Geographic analysis completed ({len(country_results)} countries)")
        return self.tables['geographic_analysis']

    def create_publication_figures(self):
        """Create publication-ready figures for manuscript."""
        print("\n" + "="*60)
        print("CREATING PUBLICATION FIGURES")
        print("="*60)

        # Figure 1: Scatterplot - Diversity vs Excellence
        if ('International_Diversity_Composite' in self.df_clean.columns and
            'Overall_Score' in self.df_clean.columns):

            fig, ax = plt.subplots(figsize=(10, 8))

            # Prepare data
            plot_data = self.df_clean[['International_Diversity_Composite', 'Overall_Score']].dropna()

            if len(plot_data) > 0:
                # Scatterplot
                ax.scatter(plot_data['International_Diversity_Composite'],
                          plot_data['Overall_Score'],
                          alpha=0.6, s=50, color='#2E86AB')

                # Regression line
                z = np.polyfit(plot_data['International_Diversity_Composite'],
                              plot_data['Overall_Score'], 1)
                p = np.poly1d(z)
                ax.plot(plot_data['International_Diversity_Composite'],
                       p(plot_data['International_Diversity_Composite']),
                       "r--", alpha=0.8, linewidth=2)

                # Calculate and display correlation
                r, p_val = pearsonr(plot_data['International_Diversity_Composite'],
                                   plot_data['Overall_Score'])

                ax.text(0.05, 0.95, f'r = {r:.3f}, p < 0.001' if p_val < 0.001 else f'r = {r:.3f}, p = {p_val:.3f}',
                       transform=ax.transAxes, fontsize=12,
                       bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))

                ax.set_xlabel('International Student Diversity Score', fontsize=14)
                ax.set_ylabel('Overall University Excellence Score', fontsize=14)
                ax.set_title('Relationship Between International Student Diversity and University Excellence\n(N = {:,} Universities)'.format(len(plot_data)),
                           fontsize=16, pad=20)

                plt.tight_layout()
                plt.savefig('Figure_1_Diversity_Excellence_Scatterplot.png', dpi=300, bbox_inches='tight')
                plt.close()

                self.figures['Figure_1'] = 'Figure_1_Diversity_Excellence_Scatterplot.png'
                print("✓ Figure 1 created: Diversity-Excellence Scatterplot")

        # Figure 2: Box plots by institutional characteristics
        if 'Size' in self.df_clean.columns and 'Overall_Score' in self.df_clean.columns:

            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle('University Excellence by Institutional Characteristics', fontsize=16, y=0.98)

            # Size categories
            if len(self.df_clean['Size'].unique()) > 1:
                size_data = self.df_clean.dropna(subset=['Size', 'Overall_Score'])
                if len(size_data) > 0:
                    sns.boxplot(data=size_data, x='Size', y='Overall_Score', ax=axes[0,0])
                    axes[0,0].set_title('By Institution Size')
                    axes[0,0].set_xlabel('Size Category')
                    axes[0,0].set_ylabel('Overall Excellence Score')

            # Research intensity
            if 'Research' in self.df_clean.columns and len(self.df_clean['Research'].unique()) > 1:
                research_data = self.df_clean.dropna(subset=['Research', 'Overall_Score'])
                if len(research_data) > 0:
                    sns.boxplot(data=research_data, x='Research', y='Overall_Score', ax=axes[0,1])
                    axes[0,1].set_title('By Research Intensity')
                    axes[0,1].set_xlabel('Research Level')
                    axes[0,1].set_ylabel('Overall Excellence Score')

            # Focus area
            if 'Focus' in self.df_clean.columns and len(self.df_clean['Focus'].unique()) > 1:
                focus_data = self.df_clean.dropna(subset=['Focus', 'Overall_Score'])
                if len(focus_data) > 0:
                    sns.boxplot(data=focus_data, x='Focus', y='Overall_Score', ax=axes[1,0])
                    axes[1,0].set_title('By Academic Focus')
                    axes[1,0].set_xlabel('Focus Category')
                    axes[1,0].set_ylabel('Overall Excellence Score')

            # Ownership type
            if 'PrivateGovernment' in self.df_clean.columns and len(self.df_clean['PrivateGovernment'].unique()) > 1:
                ownership_data = self.df_clean.dropna(subset=['PrivateGovernment', 'Overall_Score'])
                if len(ownership_data) > 0:
                    sns.boxplot(data=ownership_data, x='PrivateGovernment', y='Overall_Score', ax=axes[1,1])
                    axes[1,1].set_title('By Ownership Type')
                    axes[1,1].set_xlabel('Ownership')
                    axes[1,1].set_ylabel('Overall Excellence Score')

            plt.tight_layout()
            plt.savefig('Figure_2_Excellence_by_Characteristics.png', dpi=300, bbox_inches='tight')
            plt.close()

            self.figures['Figure_2'] = 'Figure_2_Excellence_by_Characteristics.png'
            print("✓ Figure 2 created: Excellence by Institutional Characteristics")

        # Figure 3: Geographic distribution (Top 20 countries)
        if 'geographic_analysis' in self.tables:
            geo_data = self.tables['geographic_analysis'].head(20)

            fig, ax = plt.subplots(figsize=(12, 10))

            # Horizontal bar chart
            y_pos = np.arange(len(geo_data))
            bars = ax.barh(y_pos, geo_data['Mean_Overall_Score'], color='#A23B72', alpha=0.8)

            ax.set_yticks(y_pos)
            ax.set_yticklabels(geo_data['Country'])
            ax.set_xlabel('Mean Overall Excellence Score', fontsize=14)
            ax.set_title('University Excellence by Country\n(Top 20 Countries by Performance)', fontsize=16, pad=20)

            # Add value labels on bars
            for i, (bar, value) in enumerate(zip(bars, geo_data['Mean_Overall_Score'])):
                if not np.isnan(value):
                    ax.text(value + 1, bar.get_y() + bar.get_height()/2,
                           f'{value:.1f}', ha='left', va='center', fontsize=10)

            plt.tight_layout()
            plt.savefig('Figure_3_Geographic_Distribution.png', dpi=300, bbox_inches='tight')
            plt.close()

            self.figures['Figure_3'] = 'Figure_3_Geographic_Distribution.png'
            print("✓ Figure 3 created: Geographic Distribution")

        # Figure 4: Correlation heatmap
        if 'correlation_matrix' in self.tables:
            corr_matrix = self.tables['correlation_matrix']

            fig, ax = plt.subplots(figsize=(12, 10))

            # Create heatmap
            mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
            sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='RdBu_r', center=0,
                       square=True, linewidths=0.5, cbar_kws={"shrink": .8}, ax=ax)

            ax.set_title('Correlation Matrix: International Diversity and University Excellence Metrics',
                        fontsize=16, pad=20)

            plt.tight_layout()
            plt.savefig('Figure_4_Correlation_Heatmap.png', dpi=300, bbox_inches='tight')
            plt.close()

            self.figures['Figure_4'] = 'Figure_4_Correlation_Heatmap.png'
            print("✓ Figure 4 created: Correlation Heatmap")

        print(f"✓ All figures created ({len(self.figures)} total)")

    def export_tables_to_excel(self, filename='manuscript_tables.xlsx'):
        """Export all tables to Excel for manuscript preparation."""
        print(f"\nExporting tables to {filename}...")

        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            for table_name, table_df in self.tables.items():
                # Clean table name for sheet name
                sheet_name = table_name.replace('_', ' ').title()[:31]  # Excel sheet name limit
                table_df.to_excel(writer, sheet_name=sheet_name, index=False)

        print(f"✓ Tables exported to {filename}")

    def run_complete_analysis(self):
        """Execute complete statistical analysis for manuscript."""
        print("STARTING COMPREHENSIVE STATISTICAL ANALYSIS FOR MANUSCRIPT")
        print("="*80)

        # Load and preprocess data
        self.load_and_preprocess_data()

        # Execute all analyses
        self.descriptive_statistics()
        self.correlation_analysis()
        self.hierarchical_regression_analysis()
        self.subgroup_analysis()
        self.geographic_analysis()

        # Create visualizations
        self.create_publication_figures()

        # Export results
        self.export_tables_to_excel()

        # Print summary
        print("\n" + "="*80)
        print("ANALYSIS SUMMARY")
        print("="*80)
        print(f"Dataset: {len(self.df_clean)} universities analyzed")
        print(f"Tables generated: {len(self.tables)}")
        print(f"Figures created: {len(self.figures)}")
        print(f"Regression models: {len(self.results.get('regression_models', {}))}")

        print("\nKey findings preview:")
        if 'correlation_details' in self.tables:
            diversity_excellence_corr = self.tables['correlation_details'][
                (self.tables['correlation_details']['Variable_1'] == 'International_Diversity_Composite') &
                (self.tables['correlation_details']['Variable_2'] == 'Overall_Score')
            ]
            if not diversity_excellence_corr.empty:
                r_val = diversity_excellence_corr.iloc[0]['r']
                p_val = diversity_excellence_corr.iloc[0]['p_value']
                print(f"- International diversity-excellence correlation: r = {r_val:.3f}, p = {p_val:.3f}")

        if 'geographic_analysis' in self.tables:
            top_country = self.tables['geographic_analysis'].iloc[0]
            print(f"- Top performing country: {top_country['Country']} (Mean score: {top_country['Mean_Overall_Score']:.1f})")

        print("\n✓ Complete analysis finished - Ready for manuscript writing!")
        return self

def main():
    """Main execution function."""
    # Initialize analysis
    analyzer = ManuscriptAnalysis("2026_QS_World_University_Rankings_STANDARDIZED_cleaned_with_visible_NaN.csv")

    # Run complete analysis
    results = analyzer.run_complete_analysis()

    return results

if __name__ == "__main__":
    analysis_results = main()
