"""
CSV Data Analysis Script
Author: Dr<PERSON> <PERSON><PERSON>, Symbiosis International (Deemed University)
Created: 2025-01-08
Description: Comprehensive analysis of QS World University Rankings dataset
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def setup_visualization_style():
    """Set global styling for all visualizations."""
    plt.style.use('seaborn-v0_8-whitegrid')
    
    # Color palettes for different visualization types
    COLOR_PALETTE = {
        'categorical': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
                        '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'],
        'sequential': sns.color_palette("viridis", 10),
        'diverging': sns.color_palette("RdBu_r", 10),
        'highlight': ['#cccccc', '#cccccc', '#cccccc', '#ff7f0e', '#cccccc']
    }
    
    # Typography
    plt.rcParams['font.family'] = 'sans-serif'
    plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
    plt.rcParams['font.size'] = 12
    plt.rcParams['axes.titlesize'] = 16
    plt.rcParams['axes.titleweight'] = 'bold'
    plt.rcParams['axes.labelsize'] = 14
    plt.rcParams['xtick.labelsize'] = 12
    plt.rcParams['ytick.labelsize'] = 12
    
    # Figure sizing
    plt.rcParams['figure.figsize'] = (12, 8)
    plt.rcParams['figure.dpi'] = 100
    
    return COLOR_PALETTE

def load_and_examine_data(filepath):
    """
    Load and perform initial examination of the CSV dataset.
    
    Parameters
    ----------
    filepath : str
        Path to the CSV file
        
    Returns
    -------
    DataFrame
        Loaded dataset
    """
    print("="*80)
    print("QS WORLD UNIVERSITY RANKINGS - DATA ANALYSIS")
    print("="*80)
    print(f"Loading data from: {filepath}")
    
    try:
        # Load the dataset
        df = pd.read_csv(filepath)
        
        print(f"\n✓ Data loaded successfully!")
        print(f"Dataset shape: {df.shape[0]:,} rows × {df.shape[1]} columns")
        
        return df
        
    except Exception as e:
        print(f"❌ Error loading data: {str(e)}")
        return None

def examine_data_structure(df):
    """
    Examine the basic structure and characteristics of the dataset.
    
    Parameters
    ----------
    df : DataFrame
        The dataset to examine
    """
    print("\n" + "="*60)
    print("1. DATA STRUCTURE ANALYSIS")
    print("="*60)
    
    # Basic info
    print(f"Dataset Dimensions: {df.shape[0]:,} rows × {df.shape[1]} columns")
    print(f"Memory Usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
    
    # Column information
    print(f"\nColumn Names and Data Types:")
    print("-" * 40)
    for i, (col, dtype) in enumerate(df.dtypes.items(), 1):
        print(f"{i:2d}. {col:<35} | {str(dtype)}")
    
    # Display first few rows
    print(f"\nFirst 5 rows of the dataset:")
    print("-" * 40)
    print(df.head())
    
    # Display last few rows
    print(f"\nLast 5 rows of the dataset:")
    print("-" * 40)
    print(df.tail())

def analyze_missing_values(df):
    """
    Analyze missing values in the dataset.
    
    Parameters
    ----------
    df : DataFrame
        The dataset to analyze
    """
    print("\n" + "="*60)
    print("2. MISSING VALUES ANALYSIS")
    print("="*60)
    
    # Calculate missing values
    missing_counts = df.isnull().sum()
    missing_percentages = (missing_counts / len(df)) * 100
    
    # Create missing values summary
    missing_summary = pd.DataFrame({
        'Column': missing_counts.index,
        'Missing_Count': missing_counts.values,
        'Missing_Percentage': missing_percentages.values
    }).sort_values('Missing_Count', ascending=False)
    
    # Display missing values summary
    print("Missing Values Summary:")
    print("-" * 50)
    for _, row in missing_summary.iterrows():
        if row['Missing_Count'] > 0:
            print(f"{row['Column']:<35} | {row['Missing_Count']:>6} ({row['Missing_Percentage']:>6.2f}%)")
    
    if missing_summary['Missing_Count'].sum() == 0:
        print("✓ No missing values found in the dataset!")
    else:
        print(f"\nTotal missing values: {missing_summary['Missing_Count'].sum():,}")
        print(f"Columns with missing values: {(missing_summary['Missing_Count'] > 0).sum()}")

def analyze_data_types_and_categories(df):
    """
    Analyze data types and categorical variables.
    
    Parameters
    ----------
    df : DataFrame
        The dataset to analyze
    """
    print("\n" + "="*60)
    print("3. DATA TYPES AND CATEGORICAL ANALYSIS")
    print("="*60)
    
    # Separate columns by data type
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    categorical_cols = df.select_dtypes(include=['object']).columns.tolist()
    
    print(f"Numerical columns ({len(numeric_cols)}):")
    print("-" * 30)
    for i, col in enumerate(numeric_cols, 1):
        print(f"{i:2d}. {col}")
    
    print(f"\nCategorical columns ({len(categorical_cols)}):")
    print("-" * 30)
    for i, col in enumerate(categorical_cols, 1):
        unique_count = df[col].nunique()
        print(f"{i:2d}. {col:<35} | {unique_count:>4} unique values")
    
    # Analyze categorical variables in detail
    print(f"\nCategorical Variables Analysis:")
    print("-" * 40)
    for col in categorical_cols:
        print(f"\n{col}:")
        value_counts = df[col].value_counts().head(10)
        for value, count in value_counts.items():
            percentage = (count / len(df)) * 100
            print(f"  • {str(value):<30} | {count:>6} ({percentage:>5.1f}%)")
        
        if df[col].nunique() > 10:
            print(f"  ... and {df[col].nunique() - 10} more unique values")

def generate_descriptive_statistics(df):
    """
    Generate descriptive statistics for numerical columns.
    
    Parameters
    ----------
    df : DataFrame
        The dataset to analyze
    """
    print("\n" + "="*60)
    print("4. DESCRIPTIVE STATISTICS")
    print("="*60)
    
    # Get numerical columns
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    
    if numeric_cols:
        print("Descriptive Statistics for Numerical Columns:")
        print("-" * 50)
        
        # Generate comprehensive statistics
        stats_df = df[numeric_cols].describe()
        
        # Add additional statistics
        additional_stats = pd.DataFrame({
            'skewness': df[numeric_cols].skew(),
            'kurtosis': df[numeric_cols].kurtosis(),
            'variance': df[numeric_cols].var(),
            'range': df[numeric_cols].max() - df[numeric_cols].min()
        }).T
        
        # Combine statistics
        comprehensive_stats = pd.concat([stats_df, additional_stats])
        
        print(comprehensive_stats.round(3))
        
        # Identify potential outliers using IQR method
        print(f"\nPotential Outliers Analysis (IQR Method):")
        print("-" * 45)
        
        for col in numeric_cols:
            Q1 = df[col].quantile(0.25)
            Q3 = df[col].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)][col]
            outlier_count = len(outliers)
            outlier_percentage = (outlier_count / len(df)) * 100
            
            print(f"{col:<35} | {outlier_count:>6} outliers ({outlier_percentage:>5.1f}%)")
    
    else:
        print("No numerical columns found for statistical analysis.")

def suggest_analysis_approaches(df):
    """
    Suggest potential analysis approaches based on the dataset characteristics.
    
    Parameters
    ----------
    df : DataFrame
        The dataset to analyze
    """
    print("\n" + "="*60)
    print("5. SUGGESTED ANALYSIS APPROACHES")
    print("="*60)
    
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    categorical_cols = df.select_dtypes(include=['object']).columns.tolist()
    
    print("Based on the dataset characteristics, here are recommended analysis approaches:")
    print("-" * 70)
    
    suggestions = []
    
    # University rankings specific suggestions
    if any('rank' in col.lower() for col in df.columns):
        suggestions.append("📊 Ranking Analysis: Compare university rankings across different metrics and years")
        suggestions.append("🌍 Geographic Analysis: Analyze ranking patterns by country/region")
    
    if any('score' in col.lower() for col in df.columns):
        suggestions.append("📈 Score Distribution Analysis: Examine score distributions and correlations")
        suggestions.append("🎯 Performance Benchmarking: Identify top performers in specific categories")
    
    if len(numeric_cols) >= 2:
        suggestions.append("🔗 Correlation Analysis: Explore relationships between numerical variables")
        suggestions.append("📊 Multi-dimensional Analysis: Create scatter plots and correlation matrices")
    
    if len(categorical_cols) >= 1:
        suggestions.append("📋 Categorical Analysis: Analyze distribution patterns across categories")
        suggestions.append("📊 Cross-tabulation: Examine relationships between categorical variables")
    
    # Visualization suggestions
    viz_suggestions = [
        "📊 Bar Charts: For categorical variable distributions",
        "📈 Line Charts: For trend analysis over time",
        "🔥 Heatmaps: For correlation matrices and cross-tabulations",
        "📊 Box Plots: For outlier detection and distribution comparison",
        "🌐 Geographic Maps: If location data is available",
        "📊 Dashboard Creation: Interactive visualizations for stakeholder presentations"
    ]
    
    print("\n🔍 ANALYTICAL APPROACHES:")
    for i, suggestion in enumerate(suggestions, 1):
        print(f"{i:2d}. {suggestion}")
    
    print(f"\n📊 VISUALIZATION RECOMMENDATIONS:")
    for i, viz in enumerate(viz_suggestions, 1):
        print(f"{i:2d}. {viz}")
    
    print(f"\n💡 QUALITY ASSURANCE FOCUS:")
    qa_suggestions = [
        "🎓 Institutional Performance Metrics: Analyze key performance indicators",
        "📊 Benchmarking Analysis: Compare against peer institutions",
        "📈 Trend Analysis: Track performance changes over time",
        "🌟 Excellence Identification: Highlight areas of strength and improvement",
        "📋 Report Generation: Create comprehensive quality assessment reports"
    ]
    
    for i, qa_suggestion in enumerate(qa_suggestions, 1):
        print(f"{i:2d}. {qa_suggestion}")

def main():
    """Main execution function."""
    # Setup visualization style
    COLORS = setup_visualization_style()
    
    # File path
    filepath = "2026_QS_World_University_Rankings_STANDARDIZED_cleaned_with_visible_NaN.csv"
    
    # Load and examine data
    df = load_and_examine_data(filepath)
    
    if df is not None:
        # Perform comprehensive analysis
        examine_data_structure(df)
        analyze_missing_values(df)
        analyze_data_types_and_categories(df)
        generate_descriptive_statistics(df)
        suggest_analysis_approaches(df)
        
        print("\n" + "="*80)
        print("ANALYSIS COMPLETE")
        print("="*80)
        print("The dataset has been thoroughly analyzed. You can now proceed with")
        print("specific visualizations or deeper analysis based on the suggestions above.")
        print("="*80)
        
        return df
    
    return None

if __name__ == "__main__":
    dataset = main()
